/**
 * 项目控制器 - API网关
 * 代理项目服务的API请求
 */
import { Controller, Get, Post, Put, Delete, Param, Body, Request, UseGuards, Inject } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { ClientProxy } from '@nestjs/microservices';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { firstValueFrom } from 'rxjs';

@ApiTags('项目')
@Controller('projects')
export class ProjectsController {
  constructor(
    @Inject('PROJECT_SERVICE') private readonly projectService: ClientProxy,
  ) {}

  @Get()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取所有项目' })
  @ApiResponse({ status: 200, description: '返回所有项目' })
  async findAll(@Request() req) {
    try {
      const projects = await firstValueFrom(
        this.projectService.send({ cmd: 'findAllProjects' }, { userId: req.user.id })
      );
      return { data: projects };
    } catch (error) {
      console.error('获取项目列表失败:', error);
      throw error;
    }
  }

  @Get(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '根据ID获取项目' })
  @ApiResponse({ status: 200, description: '返回项目信息' })
  async findOne(@Param('id') id: string, @Request() req) {
    try {
      const project = await firstValueFrom(
        this.projectService.send({ cmd: 'findProjectById' }, { id, userId: req.user.id })
      );
      return { data: project };
    } catch (error) {
      console.error('获取项目详情失败:', error);
      throw error;
    }
  }

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '创建项目' })
  @ApiResponse({ status: 201, description: '项目创建成功' })
  async create(@Body() createProjectDto: any, @Request() req) {
    try {
      const project = await firstValueFrom(
        this.projectService.send({ cmd: 'createProject' }, {
          userId: req.user.id,
          ...createProjectDto
        })
      );
      return { data: project };
    } catch (error) {
      console.error('创建项目失败:', error);
      throw error;
    }
  }

  @Put(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新项目' })
  @ApiResponse({ status: 200, description: '项目更新成功' })
  async update(@Param('id') id: string, @Body() updateProjectDto: any, @Request() req) {
    try {
      const project = await firstValueFrom(
        this.projectService.send({ cmd: 'updateProject' }, {
          id,
          userId: req.user.id,
          updateData: updateProjectDto
        })
      );
      return { data: project };
    } catch (error) {
      console.error('更新项目失败:', error);
      throw error;
    }
  }

  @Delete(':id')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '删除项目' })
  @ApiResponse({ status: 200, description: '项目删除成功' })
  async remove(@Param('id') id: string, @Request() req) {
    try {
      await firstValueFrom(
        this.projectService.send({ cmd: 'deleteProject' }, {
          id,
          userId: req.user.id
        })
      );
      return {
        data: {
          success: true,
          message: '项目删除成功'
        }
      };
    } catch (error) {
      console.error('删除项目失败:', error);
      throw error;
    }
  }

  @Get(':id/assets')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '获取项目资产' })
  @ApiResponse({ status: 200, description: '返回项目资产' })
  async getAssets(@Param('id') id: string, @Request() req) {
    // 暂时返回空数组，等待资产服务集成
    return {
      data: []
    };
  }
}
