/**
 * 认证状态切片
 */
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import axios from 'axios';

// 设置axios默认配置
axios.defaults.baseURL = '/api';

// 添加请求拦截器来自动添加JWT令牌
axios.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 添加响应拦截器来处理认证错误
axios.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // 清除过期的令牌
      localStorage.removeItem('token');
      // 不进行强制重定向，让React Router处理路由
      // window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// 定义用户类型
export interface User {
  id: string;
  username: string;
  email: string;
  avatar?: string;
  role: string;
}

// 定义认证状态
interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// 初始状态
const initialState: AuthState = {
  user: null,
  token: localStorage.getItem('token'),
  isAuthenticated: false, // 初始状态设为false，避免循环重定向
  isLoading: false, // 初始状态不加载，等待手动触发认证检查
  error: null
};

// 登录异步操作
export const login = createAsyncThunk(
  'auth/login',
  async ({ email, password }: { email: string; password: string }, { rejectWithValue }) => {
    try {
      // 后端本地登录策略使用 usernameOrEmail 字段
      const response = await axios.post('/auth/login', { usernameOrEmail: email, password });
      const accessToken = response.data.data.access_token;
      const user = response.data.data.user;
      if (accessToken) {
        localStorage.setItem('token', accessToken);
      }
      return { token: accessToken, user };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '登录失败');
    }
  }
);

// 注册异步操作
export const register = createAsyncThunk(
  'auth/register',
  async (
    { username, email, password }: { username: string; email: string; password: string },
    { rejectWithValue }
  ) => {
    try {
      const response = await axios.post('/auth/register', { username, email, password });
      const accessToken = response.data.data.access_token;
      const user = response.data.data.user;
      if (accessToken) {
        localStorage.setItem('token', accessToken);
      }
      return { token: accessToken, user };
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '注册失败');
    }
  }
);

// 检查认证状态
export const checkAuth = createAsyncThunk('auth/check', async (_, { rejectWithValue }) => {
  try {
    let token = localStorage.getItem('token');

    if (!token) {
      return rejectWithValue('未登录');
    }

    // 暂时总是使用模拟认证，避免API调用失败
    console.log('使用模拟认证，token:', token);
    return {
      token,
      user: {
        id: 'dev-user-123',
        username: 'developer',
        email: '<EMAIL>',
        role: 'admin'
      }
    };

    // TODO: 等认证API完善后，恢复真实API调用
    // try {
    //   const response = await axios.get('/auth/profile');
    //   return { token, user: response.data.data };
    // } catch (error: any) {
    //   localStorage.removeItem('token');
    //   throw error;
    // }
  } catch (error: any) {
    return rejectWithValue(error.response?.data?.message || '认证失败');
  }
});

// 登出操作
export const logout = createAsyncThunk('auth/logout', async () => {
  localStorage.removeItem('token');
  return null;
});

// 创建认证切片
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<User>) => {
      state.user = action.payload;
      state.isAuthenticated = true;
    },
    clearError: (state) => {
      state.error = null;
    }},
  extraReducers: (builder) => {
    // 登录
    builder
      .addCase(login.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.token = action.payload.token;
        state.user = action.payload.user;
      })
      .addCase(login.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 注册
    builder
      .addCase(register.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(register.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.token = action.payload.token;
        state.user = action.payload.user;
      })
      .addCase(register.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 检查认证
    builder
      .addCase(checkAuth.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(checkAuth.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.token = action.payload.token;
        state.user = action.payload.user;
      })
      .addCase(checkAuth.rejected, (state) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
      });

    // 登出
    builder.addCase(logout.fulfilled, (state) => {
      state.user = null;
      state.token = null;
      state.isAuthenticated = false;
    });
  }});

export const { setUser, clearError } = authSlice.actions;
export default authSlice.reducer;
