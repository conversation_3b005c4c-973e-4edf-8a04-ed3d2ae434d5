/**
 * 登录页面
 */
import React, { useEffect } from 'react';
import { Form, Input, Button, Checkbox, Card, Typography, Divider, message, Alert } from 'antd';
import { UserOutlined, LockOutlined, GithubOutlined, GoogleOutlined, FacebookOutlined } from '@ant-design/icons';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../store';
import { login, clearError } from '../store/auth/authSlice';

const { Title, Text } = Typography;

export const LoginPage: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useAppDispatch();


  
  const { isAuthenticated, isLoading, error } = useAppSelector((state) => state.auth);
  
  // 如果已经登录，重定向到首页或来源页面
  useEffect(() => {
    if (isAuthenticated && !isLoading) {
      const from = location.state?.from || '/projects';
      console.log('登录页面：用户已认证，重定向到:', from);
      navigate(from, { replace: true });
    }
  }, [isAuthenticated, isLoading, navigate, location]);
  
  // 清除错误
  useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);
  
  // 处理登录
  const handleLogin = (values: { email: string; password: string; remember: boolean }) => {
    dispatch(login({ email: values.email, password: values.password }))
      .unwrap()
      .then(() => {
        message.success(t('auth.loginSuccess'));
      })
      .catch(() => {
        // 错误已经在状态中处理
      });
  };
  
  return (
    <div
      style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        background: '#f0f2f5'}}
    >
      <Card style={{ width: 400, boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)' }}>
        <div style={{ textAlign: 'center', marginBottom: 24 }}>
          <Title level={2} style={{ margin: 0 }}>
            {t('auth.loginTitle')}
          </Title>
          <Text type="secondary">{t('auth.loginSubtitle')}</Text>
        </div>
        
        {error && (
          <Alert
            message={error}
            type="error"
            showIcon
            style={{ marginBottom: 24 }}
            closable
            onClose={() => dispatch(clearError())}
          />
        )}
        
        <Form
          name="login"
          initialValues={{ remember: true }}
          onFinish={handleLogin}
          layout="vertical"
        >
          <Form.Item
            name="email"
            rules={[
              { required: true, message: t('auth.emailRequired') || '请输入邮箱' },
              { type: 'email', message: t('auth.emailInvalid') || '请输入有效的邮箱地址' },
            ]}
          >
            <Input
              prefix={<UserOutlined />}
              placeholder={t('auth.emailPlaceholder') || '请输入邮箱'}
              size="large"
            />
          </Form.Item>
          
          <Form.Item
            name="password"
            rules={[{ required: true, message: t('auth.passwordRequired') || '请输入密码' }]}
          >
            <Input.Password
              prefix={<LockOutlined />}
              placeholder={t('auth.passwordPlaceholder') || '请输入密码'}
              size="large"
            />
          </Form.Item>
          
          <Form.Item>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <Form.Item name="remember" valuePropName="checked" noStyle>
                <Checkbox>{t('auth.rememberMe')}</Checkbox>
              </Form.Item>
              <Link to="/forgot-password">{t('auth.forgotPassword')}</Link>
            </div>
          </Form.Item>
          
          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              size="large"
              block
              loading={isLoading}
            >
              {t('auth.login')}
            </Button>
          </Form.Item>
          
          <Form.Item style={{ textAlign: 'center', marginBottom: 0 }}>
            <Text>
              {t('auth.noAccount')}{' '}
              <Link to="/register">{t('auth.register')}</Link>
            </Text>
          </Form.Item>
        </Form>
        
        <Divider plain>{t('auth.orLoginWith')}</Divider>
        
        <div style={{ display: 'flex', justifyContent: 'center', gap: 16 }}>
          <Button icon={<GithubOutlined />} size="large" shape="circle" />
          <Button icon={<GoogleOutlined />} size="large" shape="circle" />
          <Button icon={<FacebookOutlined />} size="large" shape="circle" />
        </div>
      </Card>
    </div>
  );
};
