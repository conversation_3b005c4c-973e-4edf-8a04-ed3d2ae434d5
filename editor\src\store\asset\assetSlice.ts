/**
 * 资产状态切片
 */
import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import axios from 'axios';

// 定义资产类型
export enum AssetType {
  MODEL = 'model',
  TEXTURE = 'texture',
  MATERIAL = 'material',
  AUDIO = 'audio',
  SCRIPT = 'script',
  PREFAB = 'prefab',
  SCENE = 'scene',
  ANIMATION = 'animation',
  PARTICLE = 'particle',
  OTHER = 'other'}

// 定义资产接口
export interface Asset {
  id: string;
  name: string;
  type: AssetType;
  url: string;
  thumbnail?: string;
  metadata?: any;
  projectId: string;
  folderId?: string;
  createdAt: string;
  updatedAt: string;
}

// 定义文件夹接口
export interface Folder {
  id: string;
  name: string;
  projectId: string;
  parentId?: string;
  createdAt: string;
  updatedAt: string;
}

// 定义资产状态
interface AssetState {
  assets: Asset[];
  folders: Folder[];
  currentFolder: Folder | null;
  selectedAssets: Asset[];
  isLoading: boolean;
  error: string | null;
  uploadProgress: number;
  isUploading: boolean;
}

// 初始状态
const initialState: AssetState = {
  assets: [],
  folders: [],
  currentFolder: null,
  selectedAssets: [],
  isLoading: false,
  error: null,
  uploadProgress: 0,
  isUploading: false};

// 获取项目资产
export const fetchAssets = createAsyncThunk(
  'asset/fetchAssets',
  async ({ projectId, folderId }: { projectId: string; folderId?: string }, { rejectWithValue }) => {
    try {
      // 在开发环境中返回模拟资产数据，避免404错误
      if (process.env.NODE_ENV === 'development') {
        console.log('AssetSlice：返回模拟资产数据');
        return [
          {
            id: '1',
            name: '示例模型.fbx',
            type: 'model',
            url: '/assets/models/example.fbx',
            thumbnail: '/assets/thumbnails/example.jpg',
            size: 1024000,
            projectId,
            folderId,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            metadata: {
              format: 'fbx',
              vertices: 1000,
              faces: 500
            }
          }
        ];
      }

      // 生产环境调用真实API
      const response = await axios.get(`/api/projects/${projectId}/assets`, {
        params: { folderId }});
      return response.data;
    } catch (error: any) {
      console.warn('获取资产失败:', error);
      return rejectWithValue(error.response?.data?.message || '获取资产失败');
    }
  }
);

// 获取项目文件夹
export const fetchFolders = createAsyncThunk(
  'asset/fetchFolders',
  async ({ projectId, parentId }: { projectId: string; parentId?: string }, { rejectWithValue }) => {
    try {
      const response = await axios.get(`/api/projects/${projectId}/folders`, {
        params: { parentId }});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取文件夹失败');
    }
  }
);

// 创建文件夹
export const createFolder = createAsyncThunk(
  'asset/createFolder',
  async (
    {
      projectId,
      name,
      parentId}: {
      projectId: string;
      name: string;
      parentId?: string;
    },
    { rejectWithValue }
  ) => {
    try {
      const response = await axios.post(`/api/projects/${projectId}/folders`, {
        name,
        parentId});
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '创建文件夹失败');
    }
  }
);

// 上传资产
export const uploadAsset = createAsyncThunk(
  'asset/uploadAsset',
  async (
    {
      projectId,
      file,
      type,
      folderId}: {
      projectId: string;
      file: File;
      type: AssetType;
      folderId?: string;
    },
    { dispatch, rejectWithValue }
  ) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('type', type);
      if (folderId) {
        formData.append('folderId', folderId);
      }

      const response = await axios.post(`/api/projects/${projectId}/assets/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'},
        onUploadProgress: (progressEvent) => {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / (progressEvent.total || 1));
          dispatch(setUploadProgress(percentCompleted));
        }});

      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '上传资产失败');
    }
  }
);

// 删除资产
export const deleteAsset = createAsyncThunk(
  'asset/deleteAsset',
  async ({ projectId, assetId }: { projectId: string; assetId: string }, { rejectWithValue }) => {
    try {
      await axios.delete(`/api/projects/${projectId}/assets/${assetId}`);
      return assetId;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '删除资产失败');
    }
  }
);

// 创建资产切片
const assetSlice = createSlice({
  name: 'asset',
  initialState,
  reducers: {
    setSelectedAssets: (state, action: PayloadAction<Asset[]>) => {
      state.selectedAssets = action.payload;
    },
    setCurrentFolder: (state, action: PayloadAction<Folder | null>) => {
      state.currentFolder = action.payload;
    },
    setUploadProgress: (state, action: PayloadAction<number>) => {
      state.uploadProgress = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    }},
  extraReducers: (builder) => {
    // 获取资产
    builder
      .addCase(fetchAssets.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAssets.fulfilled, (state, action) => {
        state.isLoading = false;
        state.assets = action.payload;
      })
      .addCase(fetchAssets.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 获取文件夹
    builder
      .addCase(fetchFolders.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchFolders.fulfilled, (state, action) => {
        state.isLoading = false;
        state.folders = action.payload;
      })
      .addCase(fetchFolders.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 创建文件夹
    builder
      .addCase(createFolder.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(createFolder.fulfilled, (state, action) => {
        state.isLoading = false;
        state.folders.push(action.payload);
      })
      .addCase(createFolder.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });

    // 上传资产
    builder
      .addCase(uploadAsset.pending, (state) => {
        state.isUploading = true;
        state.uploadProgress = 0;
        state.error = null;
      })
      .addCase(uploadAsset.fulfilled, (state, action) => {
        state.isUploading = false;
        state.uploadProgress = 100;
        state.assets.push(action.payload);
      })
      .addCase(uploadAsset.rejected, (state, action) => {
        state.isUploading = false;
        state.uploadProgress = 0;
        state.error = action.payload as string;
      });

    // 删除资产
    builder
      .addCase(deleteAsset.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(deleteAsset.fulfilled, (state, action) => {
        state.isLoading = false;
        state.assets = state.assets.filter((asset) => asset.id !== action.payload);
        state.selectedAssets = state.selectedAssets.filter((asset) => asset.id !== action.payload);
      })
      .addCase(deleteAsset.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  }});

export const { setSelectedAssets, setCurrentFolder, setUploadProgress, clearError } = assetSlice.actions;
export default assetSlice.reducer;
