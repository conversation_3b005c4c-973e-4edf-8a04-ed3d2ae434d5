/**
 * HTTP客户端工具
 * 提供统一的HTTP请求方法，自动处理认证
 */
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { config } from '../config/environment';

class HttpClient {
  private instance: AxiosInstance;

  constructor() {
    // 在开发环境中使用代理，所以使用相对路径
    const baseURL = import.meta.env.DEV ? '/api' : config.apiUrl;

    this.instance = axios.create({
      baseURL,
      timeout: config.requestTimeout,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // 请求拦截器 - 自动添加认证头
    this.instance.interceptors.request.use(
      (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器 - 处理认证错误
    this.instance.interceptors.response.use(
      (response) => {
        return response;
      },
      (error) => {
        const status = error.response?.status;
        const url = error.config?.url;

        if (status === 401) {
          // 只有在访问认证相关API时才处理401错误
          // 避免因为其他API的401错误导致重定向循环
          if (url && (url.includes('/auth/') || url.includes('/profile'))) {
            console.warn('认证API失败，清除认证信息');
            localStorage.removeItem('auth_token');
            localStorage.removeItem('auth_user');

            // 在开发环境中，重新初始化开发认证
            if (config.enableDebug) {
              console.warn('重新初始化开发认证');
              this.initDevAuth();
            } else {
              // 生产环境中重定向到登录页面
              window.location.href = '/login';
            }
          } else {
            // 对于非认证API的401错误，只记录警告，不重定向
            console.warn(`API ${url} 返回401错误，可能是权限不足或API未实现`);
          }
        } else if (status === 404) {
          // 对于404错误，只记录警告，不重定向
          console.warn(`API ${url} 返回404错误，可能是API路由未实现`);
        }

        return Promise.reject(error);
      }
    );
  }

  private initDevAuth(): void {
    const payload = {
      sub: 'dev-user-123',
      username: 'developer',
      email: '<EMAIL>',
      role: 'admin',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60)
    };
    
    const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
    const payloadStr = btoa(JSON.stringify(payload));
    const signature = btoa('dev-signature');
    
    const devToken = `${header}.${payloadStr}.${signature}`;
    const devUser = {
      id: 'dev-user-123',
      username: 'developer',
      email: '<EMAIL>',
      role: 'admin'
    };
    
    localStorage.setItem('auth_token', devToken);
    localStorage.setItem('auth_user', JSON.stringify(devUser));
  }

  public async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.instance.get(url, config);
  }

  public async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.instance.post(url, data, config);
  }

  public async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.instance.put(url, data, config);
  }

  public async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.instance.patch(url, data, config);
  }

  public async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.instance.delete(url, config);
  }
}

// 创建单例实例
export const httpClient = new HttpClient();

export default httpClient;
