/**
 * 编辑器页面
 */
import React, { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Spin, message} from 'antd';
import { useTranslation } from 'react-i18next';
import { useAppDispatch, useAppSelector } from '../store';
import { fetchProjectById } from '../store/project/projectSlice';
import { loadScene } from '../store/editor/editorSlice';
import { EditorLayout } from '../components/layout/EditorLayout';

export const EditorPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  
  const { projectId, sceneId } = useParams<{ projectId: string; sceneId: string }>();
  
  const { isAuthenticated } = useAppSelector((state) => state.auth);
  const { currentProject, currentScene, isLoading: projectLoading, error: projectError } = useAppSelector((state) => state.project);
  const { isLoading: editorLoading, error: editorError } = useAppSelector((state) => state.editor);
  
  const [isInitialized, setIsInitialized] = useState(false);
  
  // 检查认证状态
  useEffect(() => {
    if (!isAuthenticated && !isLoading) {
      console.log('编辑器页面：用户未认证，重定向到登录页面');
      navigate('/login', { state: { from: `/editor/${projectId}/${sceneId}` } });
    }
  }, [isAuthenticated, isLoading, navigate, projectId, sceneId]);
  
  // 加载项目和场景
  useEffect(() => {
    console.log('编辑器页面加载，参数:', { projectId, sceneId, isAuthenticated });

    if (!projectId || !sceneId) {
      console.log('缺少项目ID或场景ID，重定向到项目页面');
      navigate('/projects');
      return;
    }

    if (!isAuthenticated) {
      console.log('用户未认证，等待认证完成');
      return;
    }

    console.log('开始加载项目和场景数据');

    // 加载项目
    dispatch(fetchProjectById(projectId))
      .unwrap()
      .then(() => {
        console.log('项目加载成功，开始加载场景');
        // 加载场景
        return dispatch(loadScene({ projectId, sceneId })).unwrap();
      })
      .then(() => {
        console.log('场景加载成功，初始化完成');
        setIsInitialized(true);
      })
      .catch((error) => {
        console.error('加载项目或场景失败:', error);
        message.error(error || t('editor.loadError'));
        navigate('/projects');
      });
  }, [dispatch, navigate, projectId, sceneId, t, isAuthenticated]);
  
  // 处理错误
  useEffect(() => {
    if (projectError) {
      message.error(projectError);
    }
    
    if (editorError) {
      message.error(editorError);
    }
  }, [projectError, editorError]);
  
  // 处理离开编辑器
  const handleBeforeUnload = (e: BeforeUnloadEvent) => {
    const message = t('editor.unsavedChanges');
    e.returnValue = message;
    return message;
  };
  
  // 添加离开提示
  useEffect(() => {
    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);
  
  // 如果正在加载，显示加载状态
  if (projectLoading || editorLoading || !isInitialized) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" tip={t('editor.loading')} />
      </div>
    );
  }
  
  // 如果没有项目或场景，显示错误
  if (!currentProject || !currentScene) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <div style={{ textAlign: 'center' }}>
          <h2>{t('editor.loadError')}</h2>
          <p>{t('editor.projectOrSceneNotFound')}</p>
          <button onClick={() => navigate('/projects')}>{t('editor.backToProjects')}</button>
        </div>
      </div>
    );
  }
  
  return (
    <EditorLayout projectId={projectId!} sceneId={sceneId!} />
  );
};
